<?php
session_start();
require("../database.php");
require_once __DIR__ . '/MAILER/vendor/autoload.php';
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

// Check if user is logged in and has appropriate permissions
$adminisLoggedIn = isset($_SESSION['profile_email']);
if (!$adminisLoggedIn) {
    header('Content-Type: application/json');
    die(json_encode(['error' => 'Unauthorized access']));
}

// Get parameters
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
$round_number = isset($_GET['round_number']) ? $_GET['round_number'] : '';
$flight_list_name = isset($_GET['flight_list_name']) ? $_GET['flight_list_name'] : '';

if (!$event_mgm_id || !$round_number || !$flight_list_name) {
    header('Content-Type: application/json');
    die(json_encode(['error' => 'Missing required parameters']));
}

// --- Fetch Event Name and End Date ---
$event_info_query = "
    SELECT event_name, event_end_date
    FROM event_mgm
    WHERE event_mgm_id = ?
    LIMIT 1";
$stmt_event_info = $conn->prepare($event_info_query);
$stmt_event_info->bind_param("s", $event_mgm_id);
$stmt_event_info->execute();
$event_info_result = $stmt_event_info->get_result();
$event_info_row = $event_info_result->fetch_assoc();
$event_name = $event_info_row['event_name'] ?? 'Unknown Event';
$event_end_date = $event_info_row['event_end_date'] ?? null;
$stmt_event_info->close();
// --- End Fetch Event Name and End Date ---


// First, get the event_ids associated with event_mgm_id
$event_query = "
    SELECT event_id 
    FROM event_mgm 
    WHERE event_mgm_id = ?";
$stmt_event = $conn->prepare($event_query);
if (!$stmt_event) {
    error_log("Failed to prepare event query: " . $conn->error);
    die("Failed to prepare event query");
}
$stmt_event->bind_param("s", $event_mgm_id);
if (!$stmt_event->execute()) {
    error_log("Failed to execute event query: " . $stmt_event->error);
    die("Failed to execute event query");
}
$event_result = $stmt_event->get_result();

$event_ids = [];
while ($row = $event_result->fetch_assoc()) {
    $event_ids[] = $row['event_id'];
}
$stmt_event->close();

error_log("Found event_ids: " . implode(", ", $event_ids));

if (empty($event_ids)) {
    error_log("No events found for event_mgm_id: " . $event_mgm_id);
    die('No events found for this event_mgm_id');
}

// Create placeholders for IN clause
$placeholders = str_repeat('?,', count($event_ids) - 1) . '?';
$types_events = str_repeat('s', count($event_ids)); // Types for event_ids

// --- Fetch Max Hole Number from custom_hole ---
$max_hole = 18; // Default value
$hole_query = "SELECT MAX(hole_number) as max_hole FROM custom_hole WHERE event_id IN ($placeholders)"; 
$stmt_hole = $conn->prepare($hole_query);
if ($stmt_hole) {
    $stmt_hole->bind_param($types_events, ...$event_ids);
    $stmt_hole->execute();
    $hole_result = $stmt_hole->get_result();
    $hole_row = $hole_result->fetch_assoc();
    if ($hole_row && $hole_row['max_hole']) {
        $max_hole = (int)$hole_row['max_hole'];
    }
    $stmt_hole->close();
} else {
     error_log("Failed to prepare statement to fetch max hole: " . $conn->error);
}
// --- End Fetch Max Hole Number ---

// --- Fetch Interval Times from custom_hole ---
$interval_times = [];
$interval_query = "
    SELECT hole_number, interval_time 
    FROM custom_hole
    WHERE event_id IN ($placeholders)
    ORDER BY hole_number";
$stmt_interval = $conn->prepare($interval_query);
if ($stmt_interval) {
    $stmt_interval->bind_param($types_events, ...$event_ids);
    $stmt_interval->execute();
    $interval_result = $stmt_interval->get_result();
    while ($row = $interval_result->fetch_assoc()) {
        $interval_times[$row['hole_number']] = (int)$row['interval_time'];
    }
    $stmt_interval->close();
} else {
    error_log("Failed to prepare statement to fetch interval times: " . $conn->error);
}

// If no interval times found, use default
if (empty($interval_times)) {
    for ($i = 1; $i <= $max_hole; $i++) {
        $interval_times[$i] = 10; // Default 10 minutes
    }
}
// --- End Fetch Interval Times ---

// Fetch flight starting data (Tee Box, Time, Start Point, Date)
$query = "
    SELECT DISTINCT
        f.flight_name,
        f.tee_box,
        TIME_FORMAT(f.flight_time, '%H:%i') as flight_time,
        f.start_point,
        DATE_FORMAT(f.flight_date, '%Y-%m-%d') as flight_date,
        f.event_id,
        COUNT(DISTINCT f.form_id) as player_count
    FROM flight_list f
    WHERE f.event_id IN (" . implode(',', array_fill(0, count($event_ids), '?')) . ")
    AND f.round_number = ?
    AND f.flight_list_name = ?
    GROUP BY f.flight_name, f.tee_box, f.flight_time, f.start_point, f.flight_date, f.event_id
    ORDER BY 
        CAST(SUBSTRING_INDEX(f.tee_box, ' ', -1) AS SIGNED),
        f.flight_time,
        CAST(SUBSTRING_INDEX(f.flight_name, ' ', -1) AS SIGNED)";

error_log("Query parameters:");
error_log("- Event IDs: " . implode(", ", $event_ids));
error_log("- Round number: " . $round_number);
error_log("- Flight list name: " . $flight_list_name);

$stmt = $conn->prepare($query);
if (!$stmt) {
    error_log("Prepare failed: " . $conn->error . "\nQuery: " . $query);
    header('Content-Type: application/json');
    die(json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]));
}

// Bind parameters - all event_ids plus round_number and flight_list_name
$types = str_repeat('i', count($event_ids)) . 'is';
$params = array_merge($event_ids, [$round_number, $flight_list_name]);

try {
    $stmt->bind_param($types, ...$params);
} catch (Exception $e) {
    error_log("Bind param failed: " . $e->getMessage());
    header('Content-Type: application/json');
    die(json_encode(['error' => 'Failed to bind parameters: ' . $e->getMessage()]));
}

if (!$stmt->execute()) {
    error_log("Execute failed: " . $stmt->error . "\nParameters: " . json_encode($params));
    header('Content-Type: application/json');
    die(json_encode(['error' => 'Failed to execute statement: ' . $stmt->error]));
}

$result = $stmt->get_result();
error_log("Number of flights found: " . $result->num_rows);

if ($result->num_rows === 0) {
    error_log("No flights found for the given parameters");
    header('Content-Type: application/json');
    die(json_encode([
        'error' => 'No flights found',
        'parameters' => [
            'event_mgm_id' => $event_mgm_id,
            'round_number' => $round_number,
            'flight_list_name' => $flight_list_name
        ]
    ]));
}

$flight_starts = [];
$flights_by_tee = []; // Group flights by tee box
$start_point = null;
$flight_date = null;

while ($row = $result->fetch_assoc()) {
    $flight_name = $row['flight_name'];
    $tee_box = $row['tee_box'];
    $flight_time = $row['flight_time'];
    
    error_log("Processing flight: " . json_encode($row));

    $flight_starts[$flight_name] = [
        'tee_box' => $tee_box,
        'time' => $flight_time
    ];

    // Group flights by tee box for interval calculation
    if (!isset($flights_by_tee[$tee_box])) {
        $flights_by_tee[$tee_box] = [];
    }
    $flights_by_tee[$tee_box][] = [
        'flight_name' => $flight_name,
        'time' => $flight_time
    ];

    if ($start_point === null) $start_point = $row['start_point'];
    if ($flight_date === null) $flight_date = $row['flight_date'];
}
$stmt->close();

// Debug: Log the collected data
error_log("Data summary:");
error_log("- Start point: " . ($start_point ?? 'null'));
error_log("- Flight date: " . ($flight_date ?? 'null'));
error_log("- Number of flights: " . count($flight_starts));
error_log("- Tee boxes: " . implode(", ", array_keys($flights_by_tee)));
error_log("- Flight data: " . json_encode($flight_starts));

// If no data found, show error
if (empty($flight_starts)) {
    die("No flight data found for event_mgm_id: $event_mgm_id, round: $round_number, flight list: $flight_list_name");
}

// Prepare timetable data
$timetable = [];
$all_flight_names = array_keys($flight_starts);

// Custom sort for flight names (e.g., 1A, 1B, 2A, 2B, 10A, 10B)
usort($all_flight_names, function($a, $b) {
    preg_match('/^(\d+)([A-Z]*)/i', $a, $matchesA);
    preg_match('/^(\d+)([A-Z]*)/i', $b, $matchesB);
    $numA = isset($matchesA[1]) ? (int)$matchesA[1] : 0;
    $letterA = isset($matchesA[2]) ? strtoupper($matchesA[2]) : '';
    $numB = isset($matchesB[1]) ? (int)$matchesB[1] : 0;
    $letterB = isset($matchesB[2]) ? strtoupper($matchesB[2]) : '';
    if ($numA != $numB) return $numA - $numB;
    return strcmp($letterA, $letterB);
});

// Calculation of timetable data
foreach ($all_flight_names as $flight_name) {
    if (!isset($flight_starts[$flight_name])) continue;

    $start_info = $flight_starts[$flight_name];
    $start_tee_str = $start_info['tee_box'];
    $start_time_str = $start_info['time'];

    // Extract starting tee number
    $start_tee_num = 0;
    if (preg_match('/(\d+)$/', $start_tee_str, $matches)) {
        $start_tee_num = (int)$matches[1];
    } else {
        error_log("Could not extract tee number from: " . $start_tee_str . " for flight " . $flight_name);
        continue;
    }

    try {
        $current_time = new DateTime($start_time_str);
        $timetable[$flight_name] = []; // Initialize row for this flight
        $timetable[$flight_name]['start_tee'] = $start_tee_num;

        // Calculate times for all holes
        $accumulated_minutes = 0;
        for ($i = 0; $i < $max_hole; $i++) {
            // Determine the actual hole number for this iteration based on start tee
            $current_hole_num = (($start_tee_num + $i - 1) % $max_hole) + 1;

            // Calculate the time for this hole using accumulated minutes
            $calculated_time = clone $current_time;
            if ($i > 0) {
                // Add the interval time of the previous hole
                $prev_hole_num = (($start_tee_num + $i - 2) % $max_hole) + 1;
                $accumulated_minutes += $interval_times[$prev_hole_num];
                $calculated_time->modify("+{$accumulated_minutes} minutes");
            }
           
            $formatted_time = $calculated_time->format('H:i');
            // Add marker ONLY to the time corresponding to the starting hole (i=0)
            if ($i == 0) {
                $formatted_time = "**" . $formatted_time . "**"; // Marker for start time
            }
            // Store the calculated time against the actual hole number
            $timetable[$flight_name][$current_hole_num] = $formatted_time;
        }
    } catch (Exception $e) {
        error_log("Error processing time for flight $flight_name: " . $e->getMessage());
    }
}

// Fetch player information for each flight
$player_query = "
    SELECT 
        fl.flight_name, 
        rf.fullname, 
        rf.handicap,
        rf.nationality,
        ec.category_name,
        fl.player_number
    FROM flight_list fl
    LEFT JOIN registration_form rf ON fl.form_id = rf.form_id
    LEFT JOIN event_category ec ON rf.category_id = ec.category_id
    WHERE fl.event_id IN (" . implode(',', array_fill(0, count($event_ids), '?')) . ")
    AND fl.round_number = ?
    AND fl.flight_list_name = ?
    ORDER BY 
        CAST(SUBSTRING_INDEX(fl.flight_name, ' ', -1) AS SIGNED),
        fl.player_number";

$player_stmt = $conn->prepare($player_query);
if (!$player_stmt) {
    error_log("Prepare failed for player query: " . $conn->error);
} else {
    // Bind parameters - all event_ids plus round_number and flight_list_name
    $types = str_repeat('i', count($event_ids)) . 'is';
    $params = array_merge($event_ids, [$round_number, $flight_list_name]);
    
    try {
        $player_stmt->bind_param($types, ...$params);
        if (!$player_stmt->execute()) {
            error_log("Execute failed for player query: " . $player_stmt->error);
        } else {
            $player_result = $player_stmt->get_result();
            $players_by_flight = [];
            
            while ($player_row = $player_result->fetch_assoc()) {
                $flight_name = $player_row['flight_name'];
                if (!isset($players_by_flight[$flight_name])) {
                    $players_by_flight[$flight_name] = [];
                }
                $players_by_flight[$flight_name][] = [
                    'name' => $player_row['fullname'] ?? 'No player assigned',
                    'handicap' => $player_row['handicap'] ?? 'N/A',
                    'nationality' => $player_row['nationality'] ?? '',
                    'category' => $player_row['category_name'] ?? 'N/A',
                    'player_number' => $player_row['player_number'] ?? 0
                ];
            }
            error_log("Fetched player information for " . count($players_by_flight) . " flights");
        }
    } catch (Exception $e) {
        error_log("Error in player query: " . $e->getMessage());
    } finally {
        $player_stmt->close();
    }
}

// Clear any output buffers and set appropriate headers
if (ob_get_length()) ob_clean();

// Set error handler for CSV generation
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("CSV Generation Error: $errstr in $errfile on line $errline");
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Failed to generate CSV file', 'details' => $errstr]);
    exit;
});

// Remove CSV output and replace with XLSX export
try {
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $rowNum = 1;

    // Remove all old header rows (event info, flight list name, round number, date)
    // Add a single merged row at the top
    // Determine if this is the final round
    $round_label = 'Round ' . $round_number;
    if ($event_end_date && $flight_date && $event_end_date == $flight_date) {
        $round_label = 'Final Round';
    }
    $sheet->mergeCells('A' . $rowNum . ':' . Coordinate::stringFromColumnIndex(3 + 9) . $rowNum);
    $sheet->setCellValue('A' . $rowNum, $event_name . ' || ' . $round_label . ' (' . ($flight_date ? date('d M Y', strtotime($flight_date)) : 'N/A') . ')');
    $sheet->getStyle('A' . $rowNum)->getFont()->setBold(true)->setSize(14);
    $sheet->getStyle('A' . $rowNum)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    $rowNum++;
    // Do NOT add any other merged row for round/date.

    // Group flights by tee
    $flights_by_tee_grouped = [];
    foreach ($all_flight_names as $flight_name) {
        if (!isset($timetable[$flight_name])) continue;
        $tee = $timetable[$flight_name]['start_tee'] ?? '?';
        if (!isset($flights_by_tee_grouped[$tee])) $flights_by_tee_grouped[$tee] = [];
        $flights_by_tee_grouped[$tee][] = $flight_name;
    }

    foreach ($flights_by_tee_grouped as $tee => $flight_names) {
        // Sort group names by group number
        usort($flight_names, function($a, $b) {
            $numA = intval(preg_replace('/\D/', '', $a));
            $numB = intval(preg_replace('/\D/', '', $b));
            return $numA - $numB;
        });
        // Tee merged row
        $sheet->mergeCells('A' . $rowNum . ':' . Coordinate::stringFromColumnIndex(3 + 9) . $rowNum);
        $sheet->setCellValue('A' . $rowNum, 'Tee ' . $tee . ' (Holes 1-9)');
        $sheet->getStyle('A' . $rowNum)->getFont()->setBold(true)->setSize(12);
        $rowNum++;

        // Table 1: Holes 1-9
        $headerRow1 = ['G', 'Players', 'Hole'];
        for ($i = 1; $i <= 9; $i++) {
            $headerRow1[] = $i;
        }
        $sheet->fromArray($headerRow1, null, 'A' . $rowNum);
        $headerStartRow = $rowNum;
        $headerEndCol = Coordinate::stringFromColumnIndex(3 + 9);
        $sheet->getStyle('A' . $rowNum . ':' . $headerEndCol . $rowNum)->getFont()->setBold(true);
        // Bold the last hole number
        $sheet->getStyle($headerEndCol . $rowNum)->getFont()->setBold(true);
        $rowNum++;

        // For the interval row (intervalRow1 and intervalRow2), set the third column to 'Start'
        $intervalRow1 = ['', '', 'Start'];
        for ($i = 1; $i <= 9; $i++) {
            $interval = $interval_times[$i] ?? 10;
            $intervalRow1[] = sprintf('0:%02d', $interval);
        }
        $sheet->fromArray($intervalRow1, null, 'A' . $rowNum);
        $intervalRowNum = $rowNum;
        // Make the 'Start' cell bold
        $sheet->getStyle('C' . $intervalRowNum)->getFont()->setBold(true);
        $rowNum++;

        $tableStartRow = $headerStartRow;
        $tableEndRow = $rowNum - 1;
        foreach ($flight_names as $flight_name) {
            $flight_data = $timetable[$flight_name];
            $player_names = [];
            if (isset($players_by_flight[$flight_name])) {
                foreach ($players_by_flight[$flight_name] as $player) {
                    $name = trim($player['name']);
                    $nameParts = preg_split('/\s+/', $name);
                    $lastName = end($nameParts);
                    $displayName = $lastName;
                    $player_names[] = $displayName;
                }
            }
            // Extract group number from group name (fix for undefined variable)
            $group_num = intval(preg_replace('/\D/', '', $flight_name));
            // Build RichText for Players cell: all bold if group_num is odd, all normal if even
            $richPlayers = new \PhpOffice\PhpSpreadsheet\RichText\RichText();
            foreach ($player_names as $idx => $displayName) {
                if ($idx > 0) {
                    $richPlayers->createText('....');
                }
                $run = $richPlayers->createTextRun($displayName);
                if ($group_num % 2 == 1) {
                    $run->getFont()->setBold(true);
                } else {
                    $run->getFont()->setBold(false);
                }
            }
            $row = [$group_num, null, ''];
            for ($i = 1; $i <= 9; $i++) {
                $time_val = '';
                if (isset($flight_data[$i])) {
                    $time_val = $flight_data[$i];
                    if (preg_match('/^\*\*(\d{2}):(\d{2})\*\*$/', $time_val, $matches)) {
                        $hour = ltrim($matches[1], '0');
                        $minute = $matches[2];
                        $time_val = $hour . ':' . $minute . '*';
                    } elseif (preg_match('/^(\d{2}):(\d{2})$/', $time_val, $matches)) {
                        $hour = ltrim($matches[1], '0');
                        $minute = $matches[2];
                        $time_val = $hour . ':' . $minute;
                    }
                }
                $row[] = $time_val;
            }
            $sheet->fromArray($row, null, 'A' . $rowNum);
            $sheet->setCellValue('B' . $rowNum, $richPlayers);
            // Bold the entire row if group_num is odd, normal if even
            if ($group_num % 2 == 1) {
                $sheet->getStyle('A' . $rowNum . ':' . $headerEndCol . $rowNum)->getFont()->setBold(true);
            } else {
                $sheet->getStyle('A' . $rowNum . ':' . $headerEndCol . $rowNum)->getFont()->setBold(false);
            }
            $rowNum++;
        }
        $tableEndRow = $rowNum - 1;
        // Calculate the correct last column for borders (A to L for 9 holes)
        $colCount = 3 + 9; // G, Players, Hole, 1-9
        $lastCol = Coordinate::stringFromColumnIndex($colCount); // 'L'
        $sheet->getStyle('A' . $tableStartRow . ':' . $lastCol . $tableEndRow)
            ->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $rowNum++; // Blank row between tables

        // Tee merged row for holes 10-18
        $sheet->mergeCells('A' . $rowNum . ':' . Coordinate::stringFromColumnIndex(3 + 9) . $rowNum);
        $sheet->setCellValue('A' . $rowNum, 'Tee ' . $tee . ' (Holes 10-18)');
        $sheet->getStyle('A' . $rowNum)->getFont()->setBold(true)->setSize(12);
        $rowNum++;

        // Table 2: Holes 10-18
        $headerRow2 = ['G', 'Players', 'Hole'];
        for ($i = 10; $i <= 18; $i++) {
            $headerRow2[] = $i;
        }
        $sheet->fromArray($headerRow2, null, 'A' . $rowNum);
        $headerStartRow2 = $rowNum;
        $headerEndCol2 = Coordinate::stringFromColumnIndex(3 + 9);
        $sheet->getStyle('A' . $rowNum . ':' . $headerEndCol2 . $rowNum)->getFont()->setBold(true);
        // Bold the last hole number
        $sheet->getStyle($headerEndCol2 . $rowNum)->getFont()->setBold(true);
        $rowNum++;

        // For the interval row (intervalRow1 and intervalRow2), set the third column to 'Start'
        $intervalRow2 = ['', '', 'Start'];
        for ($i = 10; $i <= 18; $i++) {
            $interval = $interval_times[$i] ?? 10;
            $intervalRow2[] = sprintf('0:%02d', $interval);
        }
        $sheet->fromArray($intervalRow2, null, 'A' . $rowNum);
        $intervalRowNum2 = $rowNum;
        // Make the 'Start' cell bold
        $sheet->getStyle('C' . $intervalRowNum2)->getFont()->setBold(true);
        $rowNum++;

        $tableStartRow2 = $headerStartRow2;
        $tableEndRow2 = $rowNum - 1;
        foreach ($flight_names as $flight_name) {
            $flight_data = $timetable[$flight_name];
            $player_names = [];
            if (isset($players_by_flight[$flight_name])) {
                foreach ($players_by_flight[$flight_name] as $player) {
                    $name = trim($player['name']);
                    $nameParts = preg_split('/\s+/', $name);
                    $lastName = end($nameParts);
                    $displayName = $lastName;
                    $player_names[] = $displayName;
                }
            }
            // Extract group number from group name (fix for undefined variable)
            $group_num = intval(preg_replace('/\D/', '', $flight_name));
            // Build RichText for Players cell: all bold if group_num is odd, all normal if even
            $richPlayers = new \PhpOffice\PhpSpreadsheet\RichText\RichText();
            foreach ($player_names as $idx => $displayName) {
                if ($idx > 0) {
                    $richPlayers->createText('....');
                }
                $run = $richPlayers->createTextRun($displayName);
                if ($group_num % 2 == 1) {
                    $run->getFont()->setBold(true);
                } else {
                    $run->getFont()->setBold(false);
                }
            }
            $row = [$group_num, null, ''];
            for ($i = 10; $i <= 18; $i++) {
                $time_val = '';
                if (isset($flight_data[$i])) {
                    $time_val = $flight_data[$i];
                    if (preg_match('/^\*\*(\d{2}):(\d{2})\*\*$/', $time_val, $matches)) {
                        $hour = ltrim($matches[1], '0');
                        $minute = $matches[2];
                        $time_val = $hour . ':' . $minute . '*';
                    } elseif (preg_match('/^(\d{2}):(\d{2})$/', $time_val, $matches)) {
                        $hour = ltrim($matches[1], '0');
                        $minute = $matches[2];
                        $time_val = $hour . ':' . $minute;
                    }
                }
                $row[] = $time_val;
            }
            $sheet->fromArray($row, null, 'A' . $rowNum);
            $sheet->setCellValue('B' . $rowNum, $richPlayers);
            // Bold the entire row if group_num is odd, normal if even
            if ($group_num % 2 == 1) {
                $sheet->getStyle('A' . $rowNum . ':' . $headerEndCol2 . $rowNum)->getFont()->setBold(true);
            } else {
                $sheet->getStyle('A' . $rowNum . ':' . $headerEndCol2 . $rowNum)->getFont()->setBold(false);
            }
            $rowNum++;
        }
        $tableEndRow2 = $rowNum - 1;
        // For the back nine (holes 10-18), do the same
        $colCount2 = 3 + 9;
        $lastCol2 = Coordinate::stringFromColumnIndex($colCount2);
        $sheet->getStyle('A' . $tableStartRow2 . ':' . $lastCol2 . $tableEndRow2)
            ->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $rowNum++; // Blank row between tees
    }

    // Style: auto-size columns
    $colCount = 3 + 9;
    $lastCol = Coordinate::stringFromColumnIndex($colCount);
    for ($col = 1; $col <= $colCount; $col++) {
        $colLetter = Coordinate::stringFromColumnIndex($col);
        $sheet->getColumnDimension($colLetter)->setAutoSize(true);
    }

    // Output as XLSX
    $filename = 'PoP_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', $flight_list_name) . '_round_' . $round_number . '_' . date('Ymd_His') . '.xlsx';
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
} catch (Exception $e) {
    error_log("Error generating XLSX: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Failed to generate XLSX file',
        'message' => $e->getMessage(),
        'details' => [
            'flight_list_name' => $flight_list_name,
            'round_number' => $round_number,
            'num_flights' => count($all_flight_names)
        ]
    ]);
} finally {
    // Restore default error handler
    restore_error_handler();
}

exit();
?>